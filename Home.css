/* Color scheme based on power logo - using gold/yellow and black */
html {
    background-color: lightyellow;
}

/* Navigation styles */
.nav {
    overflow: hidden;
}

.nav a {
    float: left;
    margin: 10px;
    display: block;
    font-size: 5em;
    font-weight: bold;
    border: 1px solid black;
    padding: 10px;
    text-transform: uppercase;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.nav a:hover {
    background-color: gold;
}

.nav a.active {
    background-color: gold;
}

.nav #power-logo {
    font-size: inherit;
    text-transform: none;
    padding: 5px;
}

.nav #power-logo:hover {
    background-color: gold;
}



/* Page content styles */
.page-content {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.content-section {
    line-height: 1.6;
    font-size: 1.1em;
}

.content-section h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
    border-bottom: 2px solid gold;
    padding-bottom: 5px;
}

.content-section ul {
    margin: 15px 0;
    padding-left: 30px;
}

.content-section li {
    margin-bottom: 8px;
}

.content-section p {
    margin: 15px 0;
}

/* Footer styles */
footer {
    margin-top: auto;
    background-color: lightgrey;
    text-align: center;
    padding: 10px;
    font-size: 1em;
}