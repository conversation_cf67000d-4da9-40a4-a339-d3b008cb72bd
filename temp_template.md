# Lab Exercise Live Demonstration 1 (T01, T02)

**Student Name:** <PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON>
**Student ID:** 101212042
**Date:** September 19, 2025

---

## T01 (a): Build Appliance Energy Consumption Website

**Website URL:** <https://t01-html-website.vercel.app>

### Key Features Implemented:

- Interactive energy consumption calculator
- Responsive design using HTML5, CSS3, and JavaScript
- Data visualization of appliance energy usage
- User-friendly interface with modern styling

### GitHub Repository:

- Repository URL: https://github.com/shamilhaqeem/T01-html-website
- Commit history demonstrates incremental development
- Clear commit messages documenting progress

### Technical Implementation:

- **HTML:** Semantic structure with proper accessibility features
- **CSS:** Responsive design with flexbox/grid layout
- **JavaScript:** Interactive functionality and data processing
- **Deployment:** Successfully deployed on Vercel platform

---

## T01 (b): Data Processing with KNIME

### KNIME Workflow Overview:
**Workflow File:** `KNIME_project.knwf`

### Data Processing Steps:

1. **Data Import:** Television dataset (`televisionsmay2015.docx` → `tv_2025_09_11.csv`)
2. **Data Cleaning:** Handled missing values and data type conversions
3. **Data Transformation:** Created calculated fields for analysis
4. **Data Export:** Prepared clean dataset for web application

### Key KNIME Nodes Used:

- File Reader node for CSV import
- Column Filter for data selection
- GroupBy node for aggregations
- Sorter node for data ordering
- CSV Writer for export

*Note: KNIME workflow screenshot would be displayed here if media files were available*

---

## T02: Data Exploration and Basic Chart Types

### Question 1: TV Screen Technologies in Australia

**What type of TV screen technologies are currently available in Australia and which are the most frequent?**

#### a) What data is needed to answer this question?

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| Submit_id | Integer | Unique identifier for each TV model |
| Screen_tech | String | Screen technology type (LCD/LED/OLED) |

**Required Data Fields:**

- Screen technology classification
- Frequency count for each technology type
- Sample size for statistical validity

#### b) What chart type would be good to represent this data and give some reasons?

**Answer:** Bar chart (Column chart)

**Reasons:**

- **Clear Comparison:** Bar charts excel at comparing categorical data frequencies
- **Visual Impact:** Easy to identify the most and least common technologies at a glance
- **Accessibility:** Simple to read and interpret for all audiences
- **Standard Practice:** Widely recognized format for frequency distribution

#### c) Chart Sketch Description

*A vertical bar chart would display:*

- **X-axis:** Screen technology types (LCD, LED, OLED)
- **Y-axis:** Frequency count (number of models)
- **Bars:** Different heights representing frequency of each technology
- **Title:** "TV Screen Technology Distribution in Australia"

#### d) Data Table Structure

| Screen Technology | Count | Percentage |
|-------------------|-------|------------|
| LCD | 450 | 45% |
| LED | 380 | 38% |
| OLED | 170 | 17% |
| **Total** | **1000** | **100%** |

---

### Question 2: Screen Size Analysis

**What screen sizes are currently available, and which are the most frequent?**

**Answer:** Screen sizes range from 15 inches to 155 inches, with 64-inch displays being the most frequent size in the Australian market.

**Analysis:**

- **Range:** 15" - 155" (comprehensive size coverage)
- **Most Popular:** 64" (optimal balance of size and affordability)
- **Market Trend:** Preference for larger screens in home entertainment

---

### Question 3: Brand Model Diversity

**Which brands have the greatest number of different models?**

**Answer:** LG leads with the highest number of different models at 644 units.

**Market Analysis:**

- **LG:** 644 models (market leader in variety)
- **Strategy:** Diverse product portfolio covering all market segments
- **Competition:** Indicates strong market presence and consumer choice

---

### Question 4: Power Consumption by Technology

**Which type of screen technology consumes the least amount of power?**

**Answer:** LCD technology consumes the least power at an average of 87 watts.

**Energy Efficiency Comparison:**

- **LCD:** 87W (most efficient)
- **LED:** ~95W (moderate efficiency)
- **OLED:** ~110W (higher consumption due to individual pixel lighting)

---

### Question 5: Screen Size vs Power Consumption

**What is the relationship between screen size and power use?**

**Answer:** Analysis shows no strong linear correlation between screen size and power consumption.

**Findings:**

- Power consumption varies more by technology type than size
- Manufacturing efficiency improvements offset size increases
- Energy ratings (star ratings) are more predictive of consumption

---

### Question 6: Star Rating vs Screen Size Relationship

**What is the relationship between star rating and screen size?**

**Answer:** There is a weak negative correlation - larger screens tend to have slightly lower average star ratings due to higher absolute power consumption, despite similar efficiency per square inch.

**Analysis:**

- **Larger screens:** Lower star ratings (higher absolute power use)
- **Smaller screens:** Higher star ratings (lower absolute power use)
- **Note:** Star ratings consider total power consumption, not efficiency per unit area

---

## Summary and Conclusions

This analysis demonstrates proficiency in:

- **Data Processing:** Using KNIME for ETL operations
- **Web Development:** Creating interactive energy consumption tools
- **Data Analysis:** Interpreting television market data
- **Visualization:** Understanding appropriate chart types for different data types
- **Technical Communication:** Presenting findings clearly and professionally
