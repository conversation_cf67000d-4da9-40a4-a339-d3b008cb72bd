T01 (a): Build Appliance Energy Consumption website

Website: <https://t01-html-website.vercel.app>

T01 (b): Data Processing with KNIME

![](media/image1.png){width="6.268055555555556in"
height="3.0319444444444446in"}

T02: Data Exploration and Basic Chart Types

1.  What type of TV screen technologies are currently available in
    Australia and which are the most frequent?

    a.  What data is needed to answer this question?

  -----------------------------------------------------------------------
  Column Name                   Data Type        Description
  ----------------------- ---------------------- ------------------------
  Submit_id                      Integer         Submit ID

  Screen_tech                     String         Three different strings
                                                 (LCD/LCD(LED)OLED)
  -----------------------------------------------------------------------

b.  What chart type would be good to represent this data and give some
    reasons?

> Bar chart, because it shows the frequency clearly.

c.  Draw a sketch of the chart

> ![](media/image2.jpeg){width="3.5397265966754157in" height="4.7in"}

d.  Draw a sketch of the data table required to build the chart

> ![](media/image3.png){width="4.907003499562554in" height="1.7in"}

2.  What screen sizes are currently available, and which are the most
    frequent?

> From 15 inch to 155 inch, 64 inch is the most frequent

3.  Which brands have the greatest number of different models?

> LG has the most models at 644 units

4.  Which type of screen technology consumes the least amount of power?

> LCD is lowest at 87 watts

5.  What is the relationship between screen size and power use?

> No obvious correlation

6.  What is the relationship between star rating and screen size?

**the bigger the screen the more average power it uses**
