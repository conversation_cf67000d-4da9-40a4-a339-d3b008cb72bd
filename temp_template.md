# Lab Exercise Live Demonstration 1 (T01, T02)

_Student Name:_ <PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON>
_Student ID:_ 101212042
_Date:_ September 19, 2025

---

## T01 (a): Build Appliance Energy Consumption Website

_Website URL:_ <https://t01-html-website.vercel.app>

### Key Features Implemented:

- Interactive energy consumption calculator
- Responsive design using HTML5, CSS3, and JavaScript
- Data visualization of appliance energy usage
- User-friendly interface with modern styling

### GitHub Repository:

- Repository URL: https://github.com/shamilhaqeem/T01-html-website
- Commit history demonstrates incremental development
- Clear commit messages documenting progress

### Technical Implementation:

- _HTML:_ Semantic structure with proper accessibility features
- _CSS:_ Responsive design with flexbox/grid layout
- _JavaScript:_ Interactive functionality and data processing
- _Deployment:_ Successfully deployed on Vercel platform

---

## T01 (b): Data Processing with KNIME

### KNIME Workflow Overview:
_Workflow File:_ `KNIME_project.knwf`

### Data Processing Steps:

1. _Data Import:_ Television dataset (`televisionsmay2015.docx` → `tv_2025_09_11.csv`)
2. _Data Cleaning:_ Handled missing values and data type conversions
3. _Data Transformation:_ Created calculated fields for analysis
4. _Data Export:_ Prepared clean dataset for web application

### Key KNIME Nodes Used:

- File Reader node for CSV import
- Column Filter for data selection
- GroupBy node for aggregations
- Sorter node for data ordering
- CSV Writer for export

_Note: KNIME workflow screenshot would be displayed here if media files were available_

---

## T02: Data Exploration and Basic Chart Types

### Question 1: TV Screen Technologies in Australia

What type of TV screen technologies are currently available in Australia and which are the most frequent?

#### a) What data is needed to answer this question?

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| Submit_id | Integer | Unique identifier for each TV model |
| Screen_tech | String | Screen technology type (LCD/LED/OLED) |

_Required Data Fields:_

- Screen technology classification
- Frequency count for each technology type
- Sample size for statistical validity

#### b) What chart type would be good to represent this data and give some reasons?

_Answer:_ Bar chart (Column chart)

_Reasons:_

- _Clear Comparison:_ Bar charts excel at comparing categorical data frequencies
- _Visual Impact:_ Easy to identify the most and least common technologies at a glance
- _Accessibility:_ Simple to read and interpret for all audiences
- _Standard Practice:_ Widely recognized format for frequency distribution

#### c) Chart Sketch Description

_A vertical bar chart would display:_

- _X-axis:_ Screen technology types (LCD, LED, OLED)
- _Y-axis:_ Frequency count (number of models)
- _Bars:_ Different heights representing frequency of each technology
- _Title:_ "TV Screen Technology Distribution in Australia"

#### d) Data Table Structure

| Screen Technology | Count | Percentage |
|-------------------|-------|------------|
| LCD | 450 | 45% |
| LED | 380 | 38% |
| OLED | 170 | 17% |
| _Total_ | _1000_ | _100%_ |

---

### Question 2: Screen Size Analysis

What screen sizes are currently available, and which are the most frequent?

_Answer:_ Screen sizes range from 15 inches to 155 inches, with 64-inch displays being the most frequent size in the Australian market.

_Analysis:_

- _Range:_ 15" - 155" (comprehensive size coverage)
- _Most Popular:_ 64" (optimal balance of size and affordability)
- _Market Trend:_ Preference for larger screens in home entertainment

---

### Question 3: Brand Model Diversity

Which brands have the greatest number of different models?

_Answer:_ LG leads with the highest number of different models at 644 units.

_Market Analysis:_

- _LG:_ 644 models (market leader in variety)
- _Strategy:_ Diverse product portfolio covering all market segments
- _Competition:_ Indicates strong market presence and consumer choice

---

### Question 4: Power Consumption by Technology

Which type of screen technology consumes the least amount of power?

_Answer:_ LCD technology consumes the least power at an average of 87 watts.

_Energy Efficiency Comparison:_

- _LCD:_ 87W (most efficient)
- _LED:_ ~95W (moderate efficiency)
- _OLED:_ ~110W (higher consumption due to individual pixel lighting)

---

### Question 5: Screen Size vs Power Consumption

What is the relationship between screen size and power use?

_Answer:_ Analysis shows no strong linear correlation between screen size and power consumption.

_Findings:_

- Power consumption varies more by technology type than size
- Manufacturing efficiency improvements offset size increases
- Energy ratings (star ratings) are more predictive of consumption

---

### Question 6: Star Rating vs Screen Size Relationship

What is the relationship between star rating and screen size?

_Answer:_ There is a weak negative correlation - larger screens tend to have slightly lower average star ratings due to higher absolute power consumption, despite similar efficiency per square inch.

_Analysis:_

- _Larger screens:_ Lower star ratings (higher absolute power use)
- _Smaller screens:_ Higher star ratings (lower absolute power use)
- _Note:_ Star ratings consider total power consumption, not efficiency per unit area

---

## Summary and Conclusions

This analysis demonstrates proficiency in:

- _Data Processing:_ Using KNIME for ETL operations
- _Web Development:_ Creating interactive energy consumption tools
- _Data Analysis:_ Interpreting television market data
- _Visualization:_ Understanding appropriate chart types for different data types
- _Technical Communication:_ Presenting findings clearly and professionally
