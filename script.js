// Navigation functionality
function showPage(pageId) {
  // Hide all pages
  const pages = document.querySelectorAll('.page-content'); 
  pages.forEach(page => page.style.display = 'none');

  // Show selected page
  document.getElementById(pageId + '-page').style.display = 'block';

  // Update navigation active state
  const navLinks = document.querySelectorAll('.nav-link');
  navLinks.forEach(link => link.classList.remove('active'));
  document.getElementById('nav-' + pageId).classList.add('active');
}

// Power logo click handler
document.addEventListener('DOMContentLoaded', function() {
  document.getElementById('power-logo').addEventListener('click', function(e) {
    e.preventDefault();
    showPage('home');
  });
});

// Footer copyright year update
document.addEventListener('DOMContentLoaded', function() {
  const copyrightYearElement = document.getElementById('copyright-year');
  if (copyrightYearElement) {
    copyrightYearElement.textContent = new Date().getFullYear();
  }
});
